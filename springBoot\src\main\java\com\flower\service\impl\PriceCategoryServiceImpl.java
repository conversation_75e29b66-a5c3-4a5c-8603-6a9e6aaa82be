package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.entity.PriceCategory;
import com.flower.mapper.PriceCategoryMapper;
import com.flower.service.PriceCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格分类服务实现类
 */
@Service
public class PriceCategoryServiceImpl implements PriceCategoryService {

    @Autowired
    private PriceCategoryMapper priceCategoryMapper;

    @Override
    public PageResult<PriceCategory> getPriceCategoriesByPage(Long current, Long size, String keyword, Integer status) {
        Page<PriceCategory> page = new Page<>(current, size);
        LambdaQueryWrapper<PriceCategory> wrapper = new LambdaQueryWrapper<>();

        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            wrapper.like(PriceCategory::getName, keyword)
                   .or()
                   .like(PriceCategory::getDescription, keyword);
        }

        // 状态筛选
        if (status != null) {
            wrapper.eq(PriceCategory::getStatus, status);
        }

        // 按排序顺序和创建时间排序
        wrapper.orderByAsc(PriceCategory::getSortOrder);
        wrapper.orderByDesc(PriceCategory::getCreatedAt);

        IPage<PriceCategory> result = priceCategoryMapper.selectPage(page, wrapper);
        return PageResult.of(result.getRecords(), result.getTotal(), result.getSize(), result.getCurrent());
    }

    @Override
    public PriceCategory getPriceCategoryById(Long id) {
        return priceCategoryMapper.selectById(id);
    }

    @Override
    public boolean createPriceCategory(PriceCategory priceCategory) {
        priceCategory.setCreatedAt(LocalDateTime.now());
        priceCategory.setUpdatedAt(LocalDateTime.now());
        if (priceCategory.getStatus() == null) {
            priceCategory.setStatus(1);
        }
        if (priceCategory.getSortOrder() == null) {
            priceCategory.setSortOrder(0);
        }
        return priceCategoryMapper.insert(priceCategory) > 0;
    }

    @Override
    public boolean updatePriceCategory(PriceCategory priceCategory) {
        priceCategory.setUpdatedAt(LocalDateTime.now());
        return priceCategoryMapper.updateById(priceCategory) > 0;
    }

    @Override
    public boolean deletePriceCategory(Long id) {
        return priceCategoryMapper.deleteById(id) > 0;
    }

    @Override
    public boolean updatePriceCategoryStatus(Long id, Integer status) {
        PriceCategory priceCategory = new PriceCategory();
        priceCategory.setId(id);
        priceCategory.setStatus(status);
        priceCategory.setUpdatedAt(LocalDateTime.now());
        return priceCategoryMapper.updateById(priceCategory) > 0;
    }

    @Override
    public List<PriceCategory> getAllActivePriceCategories() {
        LambdaQueryWrapper<PriceCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PriceCategory::getStatus, 1);
        wrapper.orderByAsc(PriceCategory::getSortOrder);
        return priceCategoryMapper.selectList(wrapper);
    }

    @Override
    public PriceCategory getPriceCategoryByPrice(BigDecimal price) {
        LambdaQueryWrapper<PriceCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PriceCategory::getStatus, 1);
        wrapper.le(PriceCategory::getMinPrice, price);
        wrapper.ge(PriceCategory::getMaxPrice, price);
        wrapper.orderByAsc(PriceCategory::getSortOrder);
        wrapper.last("LIMIT 1");
        return priceCategoryMapper.selectOne(wrapper);
    }
}
