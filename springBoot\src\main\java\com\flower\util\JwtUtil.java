package com.flower.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * JWT工具类
 */
@Slf4j
public class JwtUtil {
    
    private static final String SECRET_KEY = "flower-admin-secret-key";
    private static final long EXPIRATION_TIME = 86400000; // 24小时
    
    /**
     * 生成JWT token
     * @param adminId 管理员ID
     * @param username 用户名
     * @param role 角色
     * @return JWT token
     */
    public static String generateToken(Long adminId, String username, String role) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + EXPIRATION_TIME);
        
        return Jwts.builder()
                .setSubject(adminId.toString())
                .claim("username", username)
                .claim("role", role)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, SECRET_KEY)
                .compact();
    }
    
    /**
     * 从token中获取管理员ID
     * @param token JWT token
     * @return 管理员ID
     */
    public static Long getAdminIdFromToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            
            return Long.valueOf(claims.getSubject());
        } catch (Exception e) {
            log.error("解析token失败", e);
            throw new RuntimeException("Token无效");
        }
    }
    
    /**
     * 从token中获取用户名
     * @param token JWT token
     * @return 用户名
     */
    public static String getUsernameFromToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            
            return claims.get("username", String.class);
        } catch (Exception e) {
            log.error("解析token失败", e);
            throw new RuntimeException("Token无效");
        }
    }
    
    /**
     * 从token中获取角色
     * @param token JWT token
     * @return 角色
     */
    public static String getRoleFromToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            
            return claims.get("role", String.class);
        } catch (Exception e) {
            log.error("解析token失败", e);
            throw new RuntimeException("Token无效");
        }
    }
    
    /**
     * 验证token是否有效
     * @param token JWT token
     * @return 是否有效
     */
    public static boolean validateToken(String token) {
        try {
            Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            log.error("Token验证失败", e);
            return false;
        }
    }
    
    /**
     * 检查token是否过期
     * @param token JWT token
     * @return 是否过期
     */
    public static boolean isTokenExpired(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Exception e) {
            log.error("检查token过期状态失败", e);
            return true;
        }
    }
    
    /**
     * 获取token的过期时间
     * @param token JWT token
     * @return 过期时间
     */
    public static Date getExpirationDateFromToken(String token) {
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
            
            return claims.getExpiration();
        } catch (Exception e) {
            log.error("获取token过期时间失败", e);
            throw new RuntimeException("Token无效");
        }
    }
}
