package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.entity.Category;
import com.flower.entity.Flower;
import com.flower.vo.FlowerVO;
import com.flower.mapper.CategoryMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.service.FlowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 花卉商品服务实现类
 */
@Service
public class FlowerServiceImpl implements FlowerService {

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public List<Category> getAllCategories() {
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Category::getStatus, 1);
        wrapper.orderByAsc(Category::getSortOrder);
        return categoryMapper.selectList(wrapper);
    }

    @Override
    public PageResult<FlowerVO> getFlowersByPage(Long current, Long size, Long categoryId, String keyword) {
        Page<Flower> page = new Page<>(current, size);
        LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(Flower::getStatus, 1);

        if (categoryId != null) {
            wrapper.eq(Flower::getCategoryId, categoryId);
        }

        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(Flower::getName, keyword)
                    .or().like(Flower::getDescription, keyword)
                    .or().like(Flower::getTags, keyword));
        }

        wrapper.orderByDesc(Flower::getIsFeatured);
        wrapper.orderByDesc(Flower::getSalesCount);
        wrapper.orderByDesc(Flower::getCreatedAt);

        IPage<Flower> result = flowerMapper.selectPage(page, wrapper);

        // 转换为FlowerVO并填充分类名称
        List<FlowerVO> flowerVOs = result.getRecords().stream()
                .map(flower -> {
                    FlowerVO vo = FlowerVO.fromFlower(flower);
                    // 获取分类名称
                    if (flower.getCategoryId() != null) {
                        Category category = categoryMapper.selectById(flower.getCategoryId());
                        if (category != null) {
                            vo.setCategoryName(category.getName());
                        }
                    }
                    return vo;
                })
                .collect(Collectors.toList());

        return PageResult.of(flowerVOs, result.getTotal(), result.getSize(), result.getCurrent());
    }

    @Override
    public Flower getFlowerById(Long id) {
        LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Flower::getId, id);
        wrapper.eq(Flower::getStatus, 1);
        return flowerMapper.selectOne(wrapper);
    }

    @Override
    public List<Flower> getFeaturedFlowers(Integer limit) {
        LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Flower::getStatus, 1);
        wrapper.eq(Flower::getIsFeatured, 1);
        wrapper.orderByDesc(Flower::getSalesCount);
        wrapper.orderByDesc(Flower::getCreatedAt);
        wrapper.last("LIMIT " + (limit != null ? limit : 10));
        return flowerMapper.selectList(wrapper);
    }

    @Override
    public PageResult<FlowerVO> searchFlowers(String keyword, Long current, Long size) {
        return getFlowersByPage(current, size, null, keyword);
    }

    @Override
    public PageResult<FlowerVO> getFlowersByCategory(Long categoryId, Long current, Long size) {
        return getFlowersByPage(current, size, categoryId, null);
    }

    @Override
    public List<String> getHotKeywords() {
        // 这里可以根据实际需求从数据库获取热门搜索关键词
        // 目前返回一些预设的热门关键词
        List<String> hotKeywords = new ArrayList<>();
        hotKeywords.add("玫瑰");
        hotKeywords.add("百合");
        hotKeywords.add("康乃馨");
        hotKeywords.add("向日葵");
        hotKeywords.add("郁金香");
        hotKeywords.add("满天星");
        hotKeywords.add("薰衣草");
        hotKeywords.add("茉莉花");

        // 也可以从数据库中查询最受欢迎的花卉名称作为热门关键词
        // 例如：根据销量、搜索次数等统计

        return hotKeywords;
    }
}
