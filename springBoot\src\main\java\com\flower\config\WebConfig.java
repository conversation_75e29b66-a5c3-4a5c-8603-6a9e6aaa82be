package com.flower.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.io.File;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取项目根目录
        String projectPath = System.getProperty("user.dir");
        String userImagePath = "file:" + projectPath + "/src/main/resources/image/user-image/";

        System.out.println("配置用户头像静态资源路径: " + userImagePath);

        // 配置用户头像静态资源访问 - 使用绝对路径
        registry.addResourceHandler("/image/user-image/**")
                .addResourceLocations(userImagePath);

        // 配置商品图片静态资源访问
        String shopImagePath = "file:" + projectPath + "/src/main/resources/image/shop-image/";
        System.out.println("配置商品图片静态资源路径: " + shopImagePath);
        registry.addResourceHandler("/image/shop-image/**")
                .addResourceLocations(shopImagePath);

        // 配置管理员头像静态资源访问
        String adminImagePath = "file:" + projectPath + "/src/main/resources/image/admin-image/";
        System.out.println("配置管理员头像静态资源路径: " + adminImagePath);
        registry.addResourceHandler("/image/admin-image/**")
                .addResourceLocations(adminImagePath);

        // 配置旧的商品图片路径格式（兼容数据库中的旧路径）
        registry.addResourceHandler("/image/flower/**")
                .addResourceLocations(shopImagePath)
                .addResourceLocations("classpath:/image/shop-image/");

        // 添加classpath路径作为备选
        registry.addResourceHandler("/image/user-image/**")
                .addResourceLocations("classpath:/image/user-image/");
        registry.addResourceHandler("/image/shop-image/**")
                .addResourceLocations("classpath:/image/shop-image/");
        registry.addResourceHandler("/image/admin-image/**")
                .addResourceLocations("classpath:/image/admin-image/");
    }
}
