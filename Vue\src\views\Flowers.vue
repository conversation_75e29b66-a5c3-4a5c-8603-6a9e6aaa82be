<template>
  <div class="page-container">
    <div class="page-header">
      <h2 class="page-title">商品管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        添加商品
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar mb-16">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索商品名称"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.categoryId"
            placeholder="选择分类"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
          >
            <el-option label="上架" :value="1" />
            <el-option label="下架" :value="0" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            重置
          </el-button>
          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>

          <!-- 导出按钮 -->
          <el-dropdown @command="handleExport" class="export-dropdown">
            <el-button size="default" class="export-btn">
              <el-icon><Download /></el-icon>
              <span>导出</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="current">
                  <el-icon><DocumentCopy /></el-icon>
                  导出当前页
                </el-dropdown-item>
                <el-dropdown-item command="all">
                  <el-icon><FolderOpened /></el-icon>
                  导出全部数据
                </el-dropdown-item>
                <el-dropdown-item command="selected" divided>
                  <el-icon><Select /></el-icon>
                  导出已选择
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
        <el-col :span="2">
          <div class="table-info">
            <el-tag type="info" size="small">共 {{ pagination.total }} 条</el-tag>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedFlowers.length > 0" class="batch-actions">
      <div class="batch-info">
        <div class="batch-summary">
          <div class="summary-item">
            <el-icon><InfoFilled /></el-icon>
            <span class="summary-label">已选择:</span>
            <span class="summary-value">{{ selectedFlowers.length }} 个商品</span>
          </div>
          <div class="summary-item">
            <el-icon><Money /></el-icon>
            <span class="summary-label">总金额:</span>
            <span class="summary-value">¥{{ selectedSummary.totalAmount }}</span>
          </div>
          <div class="summary-item">
            <el-icon><Box /></el-icon>
            <span class="summary-label">总库存:</span>
            <span class="summary-value">{{ selectedSummary.totalStock }}</span>
          </div>
          <div class="summary-item">
            <el-icon><FolderOpened /></el-icon>
            <span class="summary-label">分类:</span>
            <span class="summary-value">{{ selectedSummary.categoryCount }} 个</span>
          </div>
          <div class="summary-item">
            <el-icon><CircleCheck /></el-icon>
            <span class="summary-label">上架:</span>
            <span class="summary-value">{{ selectedSummary.onlineCount }}</span>
          </div>
          <div class="summary-item">
            <el-icon><CircleClose /></el-icon>
            <span class="summary-label">下架:</span>
            <span class="summary-value">{{ selectedSummary.offlineCount }}</span>
          </div>
        </div>
      </div>
      <div class="batch-buttons">
        <el-button
          type="warning"
          size="small"
          @click="batchToggleStatus(0)"
          :disabled="selectedSummary.onlineCount === 0"
        >
          <el-icon><Hide /></el-icon>
          批量下架 ({{ selectedSummary.onlineCount }})
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="batchToggleStatus(1)"
          :disabled="selectedSummary.offlineCount === 0"
        >
          <el-icon><View /></el-icon>
          批量上架 ({{ selectedSummary.offlineCount }})
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="batchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 商品表格 -->
    <div class="table-container">
      <el-table
              :data="flowers"
              :loading="loading"
              stripe
              style="width: 100%;"
              :header-cell-style="{ background: '#f8f9fa', color: '#606266' }"
              empty-text="暂无商品数据"
              @selection-change="handleSelectionChange"
          >
            <!-- 多选列 -->
            <el-table-column type="selection" width="55" align="center" />

            <!-- 序号列 -->
            <el-table-column label="序号" width="80" align="center">
              <template #default="{ $index }">
                <span class="row-number">{{ (pagination.current - 1) * pagination.size + $index + 1 }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="商品图片" width="120" align="center">
              <template #default="{ row }">
                <div class="product-image">
                  <el-image
                    :src="row.mainImage"
                    :preview-src-list="[row.mainImage]"
                    style="width: 60px; height: 60px; border-radius: 8px;"
                    fit="cover"
                    :preview-teleported="true"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="name" label="商品名称" min-width="200">
              <template #default="{ row }">
                <div class="product-name">
                  <span class="name-text">{{ row.name }}</span>
                  <p class="description-text">{{ row.description || '暂无描述' }}</p>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="categoryName" label="分类" width="120" align="center">
              <template #header>
                <div class="filter-header">
                  <span>分类</span>
                  <el-dropdown @command="handleCategoryFilter" trigger="click">
                    <el-icon class="filter-icon"><Filter /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <div class="filter-dropdown">
                          <div class="filter-title">筛选分类</div>
                          <el-checkbox-group v-model="filters.category" @change="applyFilters">
                            <el-checkbox
                              v-for="category in filterOptions.categories"
                              :key="category.value"
                              :label="category.value"
                            >
                              {{ category.label }}
                            </el-checkbox>
                          </el-checkbox-group>
                          <div class="filter-actions">
                            <el-button size="small" @click="clearCategoryFilter">清空</el-button>
                          </div>
                        </div>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
              <template #default="{ row }">
                <el-tag type="primary" size="small">{{ row.categoryName }}</el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="price" label="价格" width="160" align="center">
              <template #header>
                <div class="filter-header">
                  <span>价格</span>
                  <el-dropdown @command="handlePriceFilter" trigger="click">
                    <el-icon class="filter-icon"><Filter /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <div class="filter-dropdown">
                          <div class="filter-title">筛选价格</div>
                          <el-checkbox-group v-model="filters.priceRange" @change="applyFilters">
                            <el-checkbox
                              v-for="range in filterOptions.priceRanges"
                              :key="range.label"
                              :label="range.value"
                            >
                              {{ range.label }}
                            </el-checkbox>
                          </el-checkbox-group>
                          <div class="filter-actions">
                            <el-button size="small" @click="clearPriceFilter">清空</el-button>
                          </div>
                        </div>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
              <template #default="{ row }">
                <div class="price-display">
                  <div class="current-price">¥{{ formatMoney(row.price) }}</div>
                  <div v-if="row.originalPrice && row.originalPrice > row.price" class="original-price">
                    原价: ¥{{ formatMoney(row.originalPrice) }}
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="stockQuantity" label="库存" width="100" align="center">
              <template #header>
                <div class="filter-header">
                  <span>库存</span>
                  <el-dropdown @command="handleStockFilter" trigger="click">
                    <el-icon class="filter-icon"><Filter /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <div class="filter-dropdown">
                          <div class="filter-title">筛选库存</div>
                          <el-checkbox-group v-model="filters.stockRange" @change="applyFilters">
                            <el-checkbox
                              v-for="range in filterOptions.stockRanges"
                              :key="range.label"
                              :label="range.value"
                            >
                              {{ range.label }}
                            </el-checkbox>
                          </el-checkbox-group>
                          <div class="filter-actions">
                            <el-button size="small" @click="clearStockFilter">清空</el-button>
                          </div>
                        </div>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
              <template #default="{ row }">
                <el-tag
                  :type="row.stockQuantity > 10 ? 'success' : row.stockQuantity > 0 ? 'warning' : 'danger'"
                  size="small"
                >
                  {{ row.stockQuantity }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="salesCount" label="销量" width="100" align="center">
              <template #default="{ row }">
                <el-tag type="info" size="small">{{ row.salesCount || 0 }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="tags" label="商品标签" width="180" align="center">
              <template #default="{ row }">
                <div class="tags-cell">
                  <el-tag
                    v-for="tag in getTagsArray(row.tags)"
                    :key="tag"
                    size="small"
                    type="info"
                    style="margin: 2px;"
                  >
                    {{ tag }}
                  </el-tag>
                  <span v-if="!row.tags" class="no-data">暂无标签</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="color" label="颜色" width="100" align="center">
              <template #header>
                <div class="filter-header">
                  <span>颜色</span>
                  <el-dropdown @command="handleColorFilter" trigger="click">
                    <el-icon class="filter-icon"><Filter /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <div class="filter-dropdown">
                          <div class="filter-title">筛选颜色</div>
                          <el-checkbox-group v-model="filters.color" @change="applyFilters">
                            <el-checkbox
                              v-for="color in filterOptions.colors"
                              :key="color.value"
                              :label="color.value"
                            >
                              <div class="color-option">
                                <div class="color-dot" :style="{ backgroundColor: getColorValue(color.label) }"></div>
                                {{ color.label }}
                              </div>
                            </el-checkbox>
                          </el-checkbox-group>
                          <div class="filter-actions">
                            <el-button size="small" @click="clearColorFilter">清空</el-button>
                          </div>
                        </div>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
              <template #default="{ row }">
                <div v-if="row.color" class="color-display">
                  <div
                    class="color-dot"
                    :style="{ backgroundColor: getColorValue(row.color) }"
                  ></div>
                  <span class="color-text">{{ row.color }}</span>
                </div>
                <span v-else class="no-data">未设置</span>
              </template>
            </el-table-column>

            <el-table-column prop="size" label="规格" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.size" type="warning" size="small">{{ row.size }}</el-tag>
                <span v-else class="no-data">未设置</span>
              </template>
            </el-table-column>

            <el-table-column prop="isFeatured" label="精选" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.isFeatured === 1 ? 'success' : 'info'" size="small">
                  {{ row.isFeatured === 1 ? '精选' : '普通' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="flowerLanguage" label="花语寓意" width="150" align="center">
              <template #default="{ row }">
                <div class="flower-language-cell">
                  <span v-if="row.flowerLanguage" class="flower-language-text">
                    {{ row.flowerLanguage }}
                  </span>
                  <span v-else class="no-data">暂无花语</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="occasion" label="适用场合" width="120" align="center">
              <template #default="{ row }">
                <div class="occasion-cell">
                  <span v-if="row.occasion" class="occasion-text">
                    {{ row.occasion }}
                  </span>
                  <span v-else class="no-data">未设置</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="careInstructions" label="养护说明" width="120" align="center">
              <template #default="{ row }">
                <div class="care-instructions-cell">
                  <el-tooltip
                    v-if="row.careInstructions"
                    :content="row.careInstructions"
                    placement="top"
                    :show-after="500"
                  >
                    <span class="care-instructions-preview">
                      {{ truncateText(row.careInstructions, 10) }}
                    </span>
                  </el-tooltip>
                  <span v-else class="no-data">暂无说明</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #header>
                <div class="filter-header">
                  <span>状态</span>
                  <el-dropdown @command="handleStatusFilter" trigger="click">
                    <el-icon class="filter-icon"><Filter /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <div class="filter-dropdown">
                          <div class="filter-title">筛选状态</div>
                          <el-checkbox-group v-model="filters.status" @change="applyFilters">
                            <el-checkbox
                              v-for="status in filterOptions.statuses"
                              :key="status.value"
                              :label="status.value"
                            >
                              {{ status.label }}
                            </el-checkbox>
                          </el-checkbox-group>
                          <div class="filter-actions">
                            <el-button size="small" @click="clearStatusFilter">清空</el-button>
                          </div>
                        </div>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                  {{ getFlowerStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="createdAt" label="创建时间" width="180" align="center">
              <template #default="{ row }">
                <span class="time-text">{{ formatDate(row.createdAt) }}</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="280" fixed="right" align="center">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    type="primary"
                    size="small"
                    @click="showEditDialog(row)"
                  >
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>

                  <el-button
                    :type="row.status === 1 ? 'warning' : 'success'"
                    size="small"
                    @click="toggleFlowerStatus(row)"
                  >
                    <el-icon v-if="row.status === 1"><Hide /></el-icon>
                    <el-icon v-else><View /></el-icon>
                    {{ row.status === 1 ? '下架' : '上架' }}
                  </el-button>

                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteFlower(row)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑商品弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑商品' : '添加商品'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入商品名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="商品分类" prop="categoryId">
          <el-select
            v-model="form.categoryId"
            placeholder="请选择商品分类"
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="商品价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入商品价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="库存数量" prop="stockQuantity">
          <el-input-number
            v-model="form.stockQuantity"
            :min="0"
            placeholder="请输入库存数量"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入商品描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="主图片" prop="mainImage">
          <ImageUpload
            type="main"
            v-model="form.mainImage"
            @change="handleMainImageChange"
          />
        </el-form-item>

        <el-form-item label="详情图片">
          <ImageUpload
            type="detail"
            v-model="detailImages"
            :max-count="10"
            @change="handleDetailImagesChange"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="原价" prop="originalPrice">
              <el-input-number
                v-model="form.originalPrice"
                :min="0"
                :precision="2"
                placeholder="请输入原价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售数量" prop="salesCount">
              <el-input-number
                v-model="form.salesCount"
                :min="0"
                placeholder="销售数量"
                style="width: 100%"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品标签" prop="tags">
          <el-input
            v-model="form.tags"
            placeholder="请输入标签，用逗号分隔，如：浪漫,爱情,表白"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="花语寓意" prop="flowerLanguage">
          <el-input
            v-model="form.flowerLanguage"
            placeholder="请输入花语寓意"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="养护说明" prop="careInstructions">
          <el-input
            v-model="form.careInstructions"
            type="textarea"
            :rows="3"
            placeholder="请输入养护说明"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="适用场合" prop="occasion">
              <el-input
                v-model="form.occasion"
                placeholder="如：情人节,表白,纪念日"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主要颜色" prop="color">
              <div class="color-input-wrapper">
                <el-select
                  v-model="form.color"
                  placeholder="请选择或输入颜色"
                  style="width: 100%"
                  filterable
                  allow-create
                  @change="handleColorChange"
                >
                  <el-option
                    v-for="color in colorOptions"
                    :key="color"
                    :label="color"
                    :value="color"
                  >
                    <div class="color-option">
                      <div
                        class="color-preview"
                        :style="{ backgroundColor: getColorValue(color) }"
                      ></div>
                      <span class="color-name">{{ color }}</span>
                      <el-icon
                        v-if="isCustomColor(color)"
                        class="delete-icon"
                        @click.stop="removeColorOption(color)"
                      >
                        <Close />
                      </el-icon>
                    </div>
                  </el-option>
                </el-select>
                <div
                  v-if="form.color"
                  class="selected-color-preview"
                  :style="{ backgroundColor: getColorValue(form.color) }"
                  :title="form.color"
                ></div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规格描述" prop="size">
              <el-select
                v-model="form.size"
                placeholder="请选择或输入规格"
                style="width: 100%"
                filterable
                allow-create
                @change="handleSizeOptionChange"
              >
                <el-option
                  v-for="size in sizeOptions"
                  :key="size"
                  :label="size"
                  :value="size"
                >
                  <div class="size-option">
                    <span class="size-name">{{ size }}</span>
                    <el-icon
                      v-if="isCustomSize(size)"
                      class="delete-icon"
                      @click.stop="removeSizeOption(size)"
                    >
                      <Close />
                    </el-icon>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否精选" prop="isFeatured">
              <el-radio-group v-model="form.isFeatured">
                <el-radio :label="1">精选</el-radio>
                <el-radio :label="0">普通</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">上架</el-radio>
                <el-radio :label="0">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Edit, Refresh, Search, Close, Hide, View, Filter, Picture, Download, ArrowDown, DocumentCopy, FolderOpened, Select } from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'
import { formatDate, formatMoney, getFlowerStatusText, exportToCSV } from '@/utils'
import ImageUpload from '@/components/ImageUpload.vue'

const loading = ref(false)
const flowers = ref([])
const categories = ref([])
const selectedFlowers = ref([])

// 弹窗相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()
const detailImages = ref([])

// 默认选项（不可删除）
const defaultColorOptions = ['红色', '粉色', '白色', '黄色', '紫色', '蓝色', '橙色', '绿色']
const defaultSizeOptions = ['小花束', '中等花束', '大花束', '单支', '花盒', '花篮']

// 颜色和规格选项
const colorOptions = ref([...defaultColorOptions])
const sizeOptions = ref([...defaultSizeOptions])

const form = reactive({
  id: null,
  name: '',
  categoryId: null,
  price: 0,
  originalPrice: null,
  stockQuantity: 0,
  salesCount: 0,
  description: '',
  mainImage: '',
  images: '',
  tags: '',
  flowerLanguage: '',
  careInstructions: '',
  occasion: '',
  color: '',
  size: '',
  isFeatured: 0,
  status: 1
})

const formRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 1, max: 100, message: '商品名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '商品价格必须大于0', trigger: 'blur' }
  ],
  stockQuantity: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存数量不能小于0', trigger: 'blur' }
  ]
}

const searchForm = reactive({
  keyword: '',
  categoryId: null,
  status: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 筛选状态
const filters = ref({
  category: [],
  status: [],
  isFeatured: [],
  color: [],
  size: [],
  priceRange: [],
  stockRange: []
})

// 筛选选项
const filterOptions = ref({
  categories: [],
  statuses: [
    { label: '上架', value: 1 },
    { label: '下架', value: 0 }
  ],
  featuredOptions: [
    { label: '精选', value: 1 },
    { label: '普通', value: 0 }
  ],
  colors: [],
  sizes: [],
  priceRanges: [],
  stockRanges: [
    { label: '缺货(0)', value: '0-0' },
    { label: '极少库存(1-5)', value: '1-5' },
    { label: '库存不足(6-10)', value: '6-10' },
    { label: '库存偏少(11-20)', value: '11-20' },
    { label: '库存适中(21-50)', value: '21-50' },
    { label: '库存充足(51-100)', value: '51-100' },
    { label: '库存丰富(101-200)', value: '101-200' },
    { label: '库存充裕(201-500)', value: '201-500' },
    { label: '库存超多(500+)', value: '500-9999' }
  ]
})

// 计算选中商品的统计信息
const selectedSummary = computed(() => {
  if (selectedFlowers.value.length === 0) {
    return {
      totalAmount: '0.00',
      totalStock: 0,
      categoryCount: 0,
      onlineCount: 0,
      offlineCount: 0
    }
  }

  // 计算总金额
  const totalAmount = selectedFlowers.value.reduce((sum, flower) => {
    return sum + (parseFloat(flower.price) || 0)
  }, 0)

  // 计算总库存
  const totalStock = selectedFlowers.value.reduce((sum, flower) => {
    return sum + (parseInt(flower.stockQuantity) || 0)
  }, 0)

  // 计算分类数量（去重）
  const categoryIds = new Set(selectedFlowers.value.map(flower => flower.categoryId))
  const categoryCount = categoryIds.size

  // 计算上架和下架数量
  const onlineCount = selectedFlowers.value.filter(flower => flower.status === 1).length
  const offlineCount = selectedFlowers.value.filter(flower => flower.status === 0).length

  return {
    totalAmount: totalAmount.toFixed(2),
    totalStock,
    categoryCount,
    onlineCount,
    offlineCount
  }
})



// 加载分类列表
const loadCategories = async () => {
  try {
    const response = await adminApi.getCategories()
    categories.value = response.data.records || response.data

    // 更新筛选选项
    filterOptions.value.categories = categories.value.map(cat => ({
      label: cat.name,
      value: cat.id
    }))
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

// 加载价格分类数据
const loadPriceCategories = async () => {
  try {
    const response = await adminApi.getActivePriceCategories()
    if (response && response.data) {
      // 更新价格筛选选项，显示价格范围
      filterOptions.value.priceRanges = response.data.map(priceCategory => {
        const minPrice = parseFloat(priceCategory.minPrice)
        const maxPrice = parseFloat(priceCategory.maxPrice)
        const maxDisplay = maxPrice >= 99999 ? '∞' : `¥${maxPrice}`

        return {
          label: `${priceCategory.name}(¥${minPrice}-${maxDisplay})`,
          value: `${priceCategory.minPrice}-${priceCategory.maxPrice}`
        }
      })
    }
  } catch (error) {
    console.error('加载价格分类失败:', error)
    ElMessage.error('加载价格分类失败')
  }
}

// 筛选方法
const applyFilters = () => {
  console.log('应用筛选:', filters.value)
  pagination.current = 1 // 重置到第一页
  loadFlowers() // 重新加载数据
}

// 清空筛选方法
const clearCategoryFilter = () => {
  filters.value.category = []
  applyFilters()
}

const clearStatusFilter = () => {
  filters.value.status = []
  applyFilters()
}

const clearPriceFilter = () => {
  filters.value.priceRange = []
  applyFilters()
}

const clearStockFilter = () => {
  filters.value.stockRange = []
  applyFilters()
}

const clearColorFilter = () => {
  filters.value.color = []
  applyFilters()
}

const clearSizeFilter = () => {
  filters.value.size = []
  applyFilters()
}

// 清空所有筛选
const clearAllFilters = () => {
  filters.value = {
    category: [],
    status: [],
    isFeatured: [],
    color: [],
    size: [],
    priceRange: [],
    stockRange: []
  }
  applyFilters()
}

// 筛选处理方法
const handleCategoryFilter = (command) => {
  console.log('分类筛选:', command)
}

const handleStatusFilter = (command) => {
  console.log('状态筛选:', command)
}

const handlePriceFilter = (command) => {
  console.log('价格筛选:', command)
}

const handleStockFilter = (command) => {
  console.log('库存筛选:', command)
}

const handleColorFilter = (command) => {
  console.log('颜色筛选:', command)
}

const handleSizeFilter = (command) => {
  console.log('规格筛选:', command)
}

// 加载商品列表
const loadFlowers = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword,
      categoryId: searchForm.categoryId,
      status: searchForm.status
    }

    // 添加筛选参数
    if (filters.value.category.length > 0) {
      params.categories = filters.value.category.join(',')
    }
    if (filters.value.status.length > 0) {
      params.statuses = filters.value.status.join(',')
    }
    if (filters.value.color.length > 0) {
      params.colors = filters.value.color.join(',')
    }
    if (filters.value.size.length > 0) {
      params.sizes = filters.value.size.join(',')
    }
    if (filters.value.priceRange.length > 0) {
      // 价格范围已经是字符串格式，直接拼接
      params.priceRanges = filters.value.priceRange.join(',')
    }
    if (filters.value.stockRange.length > 0) {
      // 库存范围已经是字符串格式，直接拼接
      params.stockRanges = filters.value.stockRange.join(',')
    }

    // 清理空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    console.log('请求参数:', params)
    const response = await adminApi.getFlowers(params)

    // 处理API响应数据
    if (response && response.data) {
      flowers.value = response.data.records || []
      pagination.total = response.data.total || 0

      console.log('商品数据:', {
        records: flowers.value.length,
        total: pagination.total,
        current: pagination.current,
        size: pagination.size
      })
    } else {
      flowers.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载商品列表失败:', error)
    ElMessage.error('加载商品列表失败')
    flowers.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadFlowers()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.categoryId = null
  searchForm.status = null

  // 清空所有筛选条件
  filters.value = {
    category: [],
    status: [],
    isFeatured: [],
    color: [],
    size: [],
    priceRange: [],
    stockRange: []
  }

  pagination.current = 1
  loadFlowers()
  ElMessage.success('搜索条件和筛选条件已重置')
}

// 刷新数据
const handleRefresh = () => {
  // 保持当前搜索条件，重新加载数据
  loadFlowers()
  ElMessage({
    message: '商品数据已刷新',
    type: 'success',
    duration: 1500,
    showClose: false
  })
}

// 辅助函数：处理标签数组
const getTagsArray = (tags) => {
  if (!tags) return []
  return tags.split(',').filter(tag => tag.trim()).map(tag => tag.trim())
}

// 辅助函数：获取颜色值
const getColorValue = (colorName) => {
  if (!colorName) return '#909399'

  // 如果是十六进制颜色代码，直接返回
  if (colorName.startsWith('#')) {
    return colorName
  }

  // 如果是RGB格式，直接返回
  if (colorName.startsWith('rgb')) {
    return colorName
  }

  // 预定义颜色映射
  const colorMap = {
    '红色': '#e74c3c',
    '粉色': '#ff6b9d',
    '白色': '#ffffff',
    '黄色': '#f1c40f',
    '紫色': '#9b59b6',
    '蓝色': '#3498db',
    '橙色': '#e67e22',
    '绿色': '#27ae60',
    '黑色': '#2c3e50',
    '灰色': '#95a5a6',
    '棕色': '#8b4513',
    '金色': '#ffd700',
    '银色': '#bdc3c7',
    '玫红': '#e91e63',
    '天蓝': '#87ceeb',
    '薄荷绿': '#98fb98',
    '淡紫': '#dda0dd',
    '珊瑚': '#ff7f50',
    '深红': '#8b0000',
    '深蓝': '#00008b',
    '深绿': '#006400',
    '浅蓝': '#add8e6',
    '浅绿': '#90ee90',
    '浅粉': '#ffb6c1',
    '深紫': '#4b0082',
    '橘红': '#ff4500',
    '青色': '#00ffff',
    '洋红': '#ff00ff',
    '柠檬黄': '#fffacd',
    '米色': '#f5f5dc',
    '卡其色': '#f0e68c',
    '橄榄色': '#808000',
    '海军蓝': '#000080',
    '栗色': '#800000',
    '茶色': '#d2691e',
    '巧克力色': '#d2691e',
    '土黄': '#daa520',
    '森林绿': '#228b22',
    '石灰绿': '#32cd32',
    '春绿': '#00ff7f',
    '青绿': '#008b8b',
    '钢蓝': '#4682b4',
    '皇家蓝': '#4169e1',
    '中紫': '#9370db',
    '深洋红': '#8b008b',
    '深粉': '#ff1493',
    '热粉': '#ff69b4',
    '番茄红': '#ff6347',
    '橙红': '#ff4500',
    '暗橙': '#ff8c00'
  }

  // 如果在预定义映射中找到，直接返回
  if (colorMap[colorName]) {
    return colorMap[colorName]
  }

  // 智能颜色识别：尝试从颜色名称中提取颜色信息
  const lowerName = colorName.toLowerCase()

  // 英文颜色名称映射
  const englishColorMap = {
    'red': '#ff0000',
    'pink': '#ffc0cb',
    'white': '#ffffff',
    'yellow': '#ffff00',
    'purple': '#800080',
    'blue': '#0000ff',
    'orange': '#ffa500',
    'green': '#008000',
    'black': '#000000',
    'gray': '#808080',
    'grey': '#808080',
    'brown': '#a52a2a',
    'gold': '#ffd700',
    'silver': '#c0c0c0',
    'cyan': '#00ffff',
    'magenta': '#ff00ff',
    'lime': '#00ff00',
    'navy': '#000080',
    'maroon': '#800000',
    'olive': '#808000',
    'teal': '#008080',
    'aqua': '#00ffff',
    'fuchsia': '#ff00ff',
    'coral': '#ff7f50',
    'salmon': '#fa8072',
    'khaki': '#f0e68c',
    'violet': '#ee82ee',
    'indigo': '#4b0082',
    'turquoise': '#40e0d0',
    'crimson': '#dc143c',
    'chocolate': '#d2691e',
    'tan': '#d2b48c',
    'beige': '#f5f5dc',
    'ivory': '#fffff0',
    'lavender': '#e6e6fa',
    'plum': '#dda0dd',
    'orchid': '#da70d6'
  }

  // 检查英文颜色名称
  for (const [englishName, colorValue] of Object.entries(englishColorMap)) {
    if (lowerName.includes(englishName)) {
      return colorValue
    }
  }

  // 检查中文颜色关键词
  const chineseColorKeywords = {
    '红': '#ff0000',
    '粉': '#ffc0cb',
    '白': '#ffffff',
    '黄': '#ffff00',
    '紫': '#800080',
    '蓝': '#0000ff',
    '橙': '#ffa500',
    '绿': '#008000',
    '黑': '#000000',
    '灰': '#808080',
    '棕': '#a52a2a',
    '金': '#ffd700',
    '银': '#c0c0c0',
    '青': '#00ffff',
    '洋红': '#ff00ff'
  }

  for (const [keyword, colorValue] of Object.entries(chineseColorKeywords)) {
    if (colorName.includes(keyword)) {
      return colorValue
    }
  }

  // 如果都没有匹配，返回一个基于字符串哈希的颜色
  return generateColorFromString(colorName)
}

// 根据字符串生成颜色
const generateColorFromString = (str) => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 生成较为鲜艳的颜色，避免过暗或过浅
  const hue = Math.abs(hash) % 360
  const saturation = 60 + (Math.abs(hash) % 40) // 60-100%
  const lightness = 45 + (Math.abs(hash) % 20)  // 45-65%

  return `hsl(${hue}, ${saturation}%, ${lightness}%)`
}

// 文本截断函数
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 处理颜色变化
const handleColorChange = (value) => {
  if (value && !colorOptions.value.includes(value)) {
    colorOptions.value.push(value)
    // 保存到本地存储
    localStorage.setItem('flowerColors', JSON.stringify(colorOptions.value))
  }
}

// 处理规格变化
const handleSizeOptionChange = (value) => {
  if (value && !sizeOptions.value.includes(value)) {
    sizeOptions.value.push(value)
    // 保存到本地存储
    localStorage.setItem('flowerSizes', JSON.stringify(sizeOptions.value))
  }
}

// 判断是否为自定义颜色
const isCustomColor = (color) => {
  return !defaultColorOptions.includes(color)
}

// 判断是否为自定义规格
const isCustomSize = (size) => {
  return !defaultSizeOptions.includes(size)
}

// 删除自定义颜色选项
const removeColorOption = (color) => {
  if (isCustomColor(color)) {
    const index = colorOptions.value.indexOf(color)
    if (index > -1) {
      colorOptions.value.splice(index, 1)
      // 更新本地存储
      const customColors = colorOptions.value.filter(c => isCustomColor(c))
      localStorage.setItem('flowerColors', JSON.stringify([...defaultColorOptions, ...customColors]))
      ElMessage.success(`已删除颜色选项：${color}`)
    }
  }
}

// 删除自定义规格选项
const removeSizeOption = (size) => {
  if (isCustomSize(size)) {
    const index = sizeOptions.value.indexOf(size)
    if (index > -1) {
      sizeOptions.value.splice(index, 1)
      // 更新本地存储
      const customSizes = sizeOptions.value.filter(s => isCustomSize(s))
      localStorage.setItem('flowerSizes', JSON.stringify([...defaultSizeOptions, ...customSizes]))
      ElMessage.success(`已删除规格选项：${size}`)
    }
  }
}

// 从本地存储加载选项
const loadStoredOptions = () => {
  try {
    const storedColors = localStorage.getItem('flowerColors')
    if (storedColors) {
      const colors = JSON.parse(storedColors)
      // 合并默认选项和存储的选项，去重
      colorOptions.value = [...new Set([...defaultColorOptions, ...colors])]
    }

    const storedSizes = localStorage.getItem('flowerSizes')
    if (storedSizes) {
      const sizes = JSON.parse(storedSizes)
      // 合并默认选项和存储的选项，去重
      sizeOptions.value = [...new Set([...defaultSizeOptions, ...sizes])]
    }
  } catch (error) {
    console.error('加载存储选项失败:', error)
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadFlowers()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadFlowers()
}

// 切换商品状态
const toggleFlowerStatus = async (flower) => {
  try {
    const action = flower.status === 1 ? '下架' : '上架'
    await ElMessageBox.confirm(`确定要${action}商品"${flower.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await adminApi.updateFlowerStatus(flower.id, flower.status === 1 ? 0 : 1)
    ElMessage.success(`${action}成功`)
    loadFlowers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换商品状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 删除商品
const deleteFlower = async (flower) => {
  try {
    await ElMessageBox.confirm(`确定要删除商品"${flower.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await adminApi.deleteFlower(flower.id)
    ElMessage.success('删除成功')
    loadFlowers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除商品失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 多选变化处理
const handleSelectionChange = (selection) => {
  selectedFlowers.value = selection
}

// 批量切换状态
const batchToggleStatus = async (status) => {
  if (selectedFlowers.value.length === 0) {
    ElMessage.warning('请先选择要操作的商品')
    return
  }

  // 筛选出需要操作的商品
  const targetFlowers = selectedFlowers.value.filter(flower => flower.status !== status)

  if (targetFlowers.length === 0) {
    const action = status === 1 ? '上架' : '下架'
    ElMessage.warning(`选中的商品都已经是${action}状态`)
    return
  }

  try {
    const action = status === 1 ? '上架' : '下架'
    await ElMessageBox.confirm(
      `确定要${action}选中的 ${targetFlowers.length} 个商品吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量更新状态
    const promises = targetFlowers.map(flower =>
      adminApi.updateFlowerStatus(flower.id, status)
    )

    await Promise.all(promises)
    ElMessage.success(`批量${action}成功，共操作 ${targetFlowers.length} 个商品`)
    selectedFlowers.value = []
    loadFlowers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedFlowers.value.length === 0) {
    ElMessage.warning('请先选择要删除的商品')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFlowers.value.length} 个商品吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    const promises = selectedFlowers.value.map(flower =>
      adminApi.deleteFlower(flower.id)
    )

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    selectedFlowers.value = []
    loadFlowers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 显示创建弹窗
const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑弹窗
const showEditDialog = async (flower) => {
  isEdit.value = true
  resetForm()

  try {
    // 加载商品详情
    const response = await adminApi.getFlowerDetail(flower.id)
    const flowerDetail = response.data

    // 设置表单数据
    form.id = flowerDetail.id
    form.name = flowerDetail.name
    form.categoryId = flowerDetail.categoryId
    form.price = flowerDetail.price
    form.originalPrice = flowerDetail.originalPrice
    form.stockQuantity = flowerDetail.stockQuantity
    form.salesCount = flowerDetail.salesCount || 0
    form.description = flowerDetail.description || ''
    form.mainImage = flowerDetail.mainImage || ''
    form.images = flowerDetail.images || ''
    form.tags = flowerDetail.tags || ''
    form.flowerLanguage = flowerDetail.flowerLanguage || ''
    form.careInstructions = flowerDetail.careInstructions || ''
    form.occasion = flowerDetail.occasion || ''
    form.color = flowerDetail.color || ''
    form.size = flowerDetail.size || ''
    form.isFeatured = flowerDetail.isFeatured !== undefined ? flowerDetail.isFeatured : 0
    form.status = flowerDetail.status !== undefined ? flowerDetail.status : 1

    // 处理详情图片
    if (flowerDetail.images) {
      try {
        detailImages.value = JSON.parse(flowerDetail.images)
      } catch (e) {
        detailImages.value = []
      }
    } else {
      detailImages.value = []
    }

    dialogVisible.value = true
  } catch (error) {
    console.error('加载商品详情失败:', error)
    ElMessage.error('加载商品详情失败')
  }
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.name = ''
  form.categoryId = null
  form.price = 0
  form.originalPrice = null
  form.stockQuantity = 0
  form.salesCount = 0
  form.description = ''
  form.mainImage = ''
  form.images = ''
  form.tags = ''
  form.flowerLanguage = ''
  form.careInstructions = ''
  form.occasion = ''
  form.color = ''
  form.size = ''
  form.isFeatured = 0
  form.status = 1
  detailImages.value = []

  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 主图片变化处理
const handleMainImageChange = (imageUrl) => {
  form.mainImage = imageUrl
}

// 详情图片变化处理
const handleDetailImagesChange = (imageUrls) => {
  detailImages.value = imageUrls
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 显示数据确认对话框
    await showDataConfirmDialog()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 显示数据确认对话框
const showDataConfirmDialog = async () => {
  try {
    // 构建确认信息
    const confirmData = buildConfirmData()

    await ElMessageBox.confirm(
      confirmData.html,
      isEdit.value ? '确认修改商品信息' : '确认创建商品',
      {
        confirmButtonText: isEdit.value ? '确认修改' : '确认创建',
        cancelButtonText: '返回修改',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'data-confirm-dialog',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // 用户确认后执行实际提交
            performSubmit()
            done()
          } else {
            done()
          }
        }
      }
    )
  } catch (error) {
    // 用户取消或关闭对话框
    console.log('用户取消提交')
  }
}

// 构建确认数据
const buildConfirmData = () => {
  const data = {
    html: `
      <div class="confirm-data-container">
        <p class="confirm-title">请仔细检查以下信息是否正确：</p>
        <div class="confirm-item">
          <span class="label">商品名称：</span>
          <span class="value">${form.name || '未填写'}</span>
        </div>
        <div class="confirm-item">
          <span class="label">商品分类：</span>
          <span class="value">${getCategoryName(form.categoryId) || '未选择'}</span>
        </div>
        <div class="confirm-item">
          <span class="label">商品价格：</span>
          <span class="value">¥${form.price || 0}</span>
          ${form.originalPrice ? `<span class="original-price">（原价：¥${form.originalPrice}）</span>` : ''}
        </div>
        <div class="confirm-item">
          <span class="label">库存数量：</span>
          <span class="value">${form.stockQuantity || 0} 件</span>
        </div>
        ${form.tags ? `
        <div class="confirm-item">
          <span class="label">商品标签：</span>
          <span class="value">${form.tags}</span>
        </div>
        ` : ''}
        ${form.color ? `
        <div class="confirm-item">
          <span class="label">主要颜色：</span>
          <span class="value">${form.color}</span>
        </div>
        ` : ''}
        ${form.size ? `
        <div class="confirm-item">
          <span class="label">规格描述：</span>
          <span class="value">${form.size}</span>
        </div>
        ` : ''}
        ${form.flowerLanguage ? `
        <div class="confirm-item">
          <span class="label">花语寓意：</span>
          <span class="value">${form.flowerLanguage}</span>
        </div>
        ` : ''}
        ${form.occasion ? `
        <div class="confirm-item">
          <span class="label">适用场合：</span>
          <span class="value">${form.occasion}</span>
        </div>
        ` : ''}
        <div class="confirm-item">
          <span class="label">是否精选：</span>
          <span class="value">${form.isFeatured === 1 ? '是' : '否'}</span>
        </div>
        <div class="confirm-item">
          <span class="label">商品状态：</span>
          <span class="value">${form.status === 1 ? '上架' : '下架'}</span>
        </div>
        <p class="confirm-warning">⚠️ 提交后将${isEdit.value ? '修改' : '创建'}商品信息，请确认无误后点击确认按钮。</p>
      </div>
    `
  }
  return data
}

// 获取分类名称
const getCategoryName = (categoryId) => {
  const category = categories.value.find(cat => cat.id === categoryId)
  return category ? category.name : ''
}

// 执行实际提交
const performSubmit = async () => {
  try {
    submitting.value = true

    // 处理详情图片
    const submitData = {
      ...form,
      detailImages: JSON.stringify(detailImages.value)
    }

    console.log('提交的数据:', submitData)

    if (isEdit.value) {
      console.log('更新商品，ID:', form.id)
      await adminApi.updateFlower(form.id, submitData)
      ElMessage.success('商品修改成功！')
    } else {
      console.log('创建新商品')
      await adminApi.createFlower(submitData)
      ElMessage.success('商品创建成功！')
    }

    dialogVisible.value = false
    await loadFlowers()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败: ' + error.message)
  } finally {
    submitting.value = false
  }
}

// 初始化
// 初始化筛选选项
const initFilterOptions = () => {
  // 从现有商品数据中提取颜色和规格选项
  const colors = [...new Set(flowers.value.map(f => f.color).filter(Boolean))]
  const sizes = [...new Set(flowers.value.map(f => f.size).filter(Boolean))]

  filterOptions.value.colors = colors.map(color => ({
    label: color,
    value: color
  }))

  filterOptions.value.sizes = sizes.map(size => ({
    label: size,
    value: size
  }))
}

// 导出商品数据为CSV
const exportFlowersToCSV = (data, filename) => {
  // CSV头部
  const headers = ['ID', '商品名称', '分类', '价格', '原价', '库存', '颜色', '规格', '状态', '销量', '创建时间']

  // 数据映射函数
  const dataMapper = (flower) => [
    flower.id,
    flower.name || '',
    flower.categoryName || '',
    `¥${formatMoney(flower.price)}`,
    `¥${formatMoney(flower.originalPrice)}`,
    flower.stock || 0,
    flower.color || '',
    flower.size || '',
    getFlowerStatusText(flower.status),
    flower.salesCount || 0,
    formatDate(flower.createTime)
  ]

  exportToCSV(data, headers, filename, dataMapper)
}

// 导出处理
const handleExport = async (command) => {
  try {
    switch (command) {
      case 'current':
        if (flowers.value.length === 0) {
          ElMessage.warning('当前页面没有商品数据')
          return
        }
        exportFlowersToCSV(flowers.value, `商品数据_第${pagination.current}页_${new Date().toLocaleDateString()}.csv`)
        ElMessage.success(`已导出当前页 ${flowers.value.length} 条商品数据`)
        break
      case 'all':
        ElMessage.info('正在导出全部数据，请稍候...')
        try {
          // 获取所有商品数据
          const response = await adminApi.getFlowers({
            current: 1,
            size: 10000, // 获取大量数据
            keyword: searchForm.keyword,
            categoryId: searchForm.categoryId,
            status: searchForm.status
          })
          const allFlowers = response.data.records || []
          if (allFlowers.length === 0) {
            ElMessage.warning('没有商品数据可导出')
            return
          }
          exportFlowersToCSV(allFlowers, `全部商品数据_${new Date().toLocaleDateString()}.csv`)
          ElMessage.success(`全部商品数据导出完成，共 ${allFlowers.length} 条`)
        } catch (error) {
          console.error('导出全部数据失败:', error)
          ElMessage.error('导出全部数据失败')
        }
        break
      case 'selected':
        if (selectedFlowers.value.length === 0) {
          ElMessage.warning('请先选择要导出的商品')
          return
        }
        exportFlowersToCSV(selectedFlowers.value, `选中商品数据_${selectedFlowers.value.length}条_${new Date().toLocaleDateString()}.csv`)
        ElMessage.success(`已导出选中的 ${selectedFlowers.value.length} 条商品数据`)
        break
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}

onMounted(async () => {
  console.log('商品管理页面已挂载，开始加载数据...')
  try {
    // 加载存储的选项
    loadStoredOptions()

    await loadCategories()
    await loadPriceCategories()
    await loadFlowers()
    initFilterOptions()
    console.log('商品数据加载完成')
  } catch (error) {
    console.error('商品数据加载失败:', error)
    ElMessage.error('商品数据加载失败: ' + error.message)
  }
})
</script>

<style scoped>
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.search-bar {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mb-16 {
  margin-bottom: 16px;
}

.table-info {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-image {
  display: flex;
  justify-content: center;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  border-radius: 8px;
  color: #c0c4cc;
}

.product-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name-text {
  font-weight: 500;
  color: #2c3e50;
}

.description-text {
  font-size: 12px;
  color: #8892b0;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
}

.price-cell {
  display: flex;
  justify-content: center;
}

.price-value {
  font-weight: 600;
  color: #e74c3c;
}

.time-text {
  color: #8892b0;
  font-size: 13px;
}

/* 弹窗样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-dialog__body {
  padding: 20px 24px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-input,
.el-select,
.el-textarea {
  width: 100%;
}

.el-input-number {
  width: 100%;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 60px;
  flex-shrink: 0;
  font-size: 12px;
  padding: 5px 8px;
}

/* 批量操作栏样式 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-info {
  flex: 1;
}

.batch-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #0369a1;
  font-size: 14px;
}

.summary-label {
  font-weight: 500;
  color: #0f172a;
}

.summary-value {
  font-weight: 600;
  color: #0369a1;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.batch-buttons .el-button {
  margin: 0;
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .batch-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .batch-summary {
    justify-content: center;
  }

  .batch-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .batch-summary {
    gap: 12px;
  }

  .summary-item {
    font-size: 13px;
  }

  .batch-buttons {
    flex-direction: column;
  }

  .batch-buttons .el-button {
    min-width: auto;
  }
}

/* 新增字段样式 */
.price-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.current-price {
  font-weight: 600;
  color: #e74c3c;
  font-size: 15px;
  line-height: 1.2;
}

.original-price {
  font-size: 11px;
  color: #95a5a6;
  text-decoration: line-through;
  line-height: 1.2;
  white-space: nowrap;
}

.tags-cell {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2px;
}

.color-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.color-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #ddd;
  flex-shrink: 0;
}

.color-text {
  font-size: 12px;
  color: #606266;
}

.no-data {
  color: #bdc3c7;
  font-size: 12px;
  font-style: italic;
}

/* 新增列样式 */
.flower-language-cell {
  max-width: 140px;
}

.flower-language-text {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.occasion-cell {
  max-width: 110px;
}

.occasion-text {
  font-size: 12px;
  color: #409eff;
  background-color: #ecf5ff;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.care-instructions-cell {
  max-width: 110px;
}

.care-instructions-preview {
  font-size: 12px;
  color: #67c23a;
  cursor: help;
  border-bottom: 1px dashed #67c23a;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.care-instructions-preview:hover {
  color: #529b2e;
  border-bottom-color: #529b2e;
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 序号样式 */
.row-number {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

/* 筛选相关样式 */
.filter-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  width: 100%;
}

.filter-icon {
  margin-left: 8px;
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;
}

.filter-icon:hover {
  color: #409EFF;
}

.filter-dropdown {
  padding: 12px;
  min-width: 200px;
}

.filter-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 8px;
}

.filter-dropdown .el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.filter-dropdown .el-checkbox {
  margin: 0;
}

.filter-actions {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #EBEEF5;
  text-align: right;
}

.color-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #ddd;
  flex-shrink: 0;
}



/* 表单布局优化 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #2c3e50;
}

/* 对话框内容区域 */
.el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 颜色选择器样式 */
.color-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  position: relative;
}

.color-preview {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #ddd;
  flex-shrink: 0;
  box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
}

.color-preview[style*="background-color: #ffffff"] {
  border: 1px solid #ccc;
}

.color-name {
  flex: 1;
}

.size-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
}

.size-name {
  flex: 1;
}

.delete-icon {
  color: #f56c6c;
  cursor: pointer;
  font-size: 12px;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.delete-icon:hover {
  background-color: #f56c6c;
  color: white;
  transform: scale(1.1);
}

.selected-color-preview {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #ddd;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selected-color-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 选择器下拉选项优化 */
.el-select-dropdown__item {
  padding: 8px 12px;
}

.el-select-dropdown__item:hover {
  background-color: #f5f7fa;
}

/* 数据确认对话框样式 */
:deep(.data-confirm-dialog) {
  .el-message-box__content {
    max-height: 60vh;
    overflow-y: auto;
  }
}

.confirm-data-container {
  text-align: left;
  font-size: 14px;
  line-height: 1.6;
}

.confirm-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  text-align: center;
}

.confirm-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.confirm-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.confirm-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.confirm-item .original-price {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
  text-decoration: line-through;
}

.confirm-warning {
  margin-top: 20px;
  padding: 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
  color: #f56c6c;
  font-weight: 500;
  text-align: center;
  font-size: 14px;
}

/* 导出按钮样式 */
.export-dropdown {
  margin-left: 8px;
}

.export-btn {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.export-btn:hover {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.dropdown-icon {
  margin-left: 4px;
  transition: transform 0.3s ease;
}

.export-dropdown.is-opened .dropdown-icon {
  transform: rotate(180deg);
}

</style>
