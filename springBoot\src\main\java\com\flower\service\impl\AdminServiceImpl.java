package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.AdminLog;
import com.flower.entity.AdminUser;
import com.flower.mapper.AdminLogMapper;
import com.flower.mapper.AdminUserMapper;
import com.flower.service.AdminService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 管理员服务实现类
 */
@Slf4j
@Service
public class AdminServiceImpl implements AdminService {

    @Autowired
    private AdminUserMapper adminUserMapper;

    @Autowired
    private AdminLogMapper adminLogMapper;

    @Value("${jwt.secret:flower-admin-secret-key}")
    private String jwtSecret;

    @Value("${jwt.expiration:86400}")
    private Long jwtExpiration;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public AdminUser login(String username, String password) {
        // 查找管理员用户
        LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AdminUser::getUsername, username);
        wrapper.eq(AdminUser::getStatus, 1); // 只查询启用的用户
        
        AdminUser adminUser = adminUserMapper.selectOne(wrapper);
        
        if (adminUser == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 验证密码 - 临时支持明文密码用于测试
        boolean passwordMatches = false;
        if (adminUser.getPassword().startsWith("$2a$")) {
            // BCrypt 加密密码
            passwordMatches = passwordEncoder.matches(password, adminUser.getPassword());
        } else {
            // 明文密码（仅用于测试）
            passwordMatches = password.equals(adminUser.getPassword());
        }

        if (!passwordMatches) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        return adminUser;
    }

    @Override
    public AdminUser verifyToken(String token) {
        try {
            log.info("验证Token: {}", token);

            Claims claims = Jwts.parser()
                    .setSigningKey(jwtSecret)
                    .parseClaimsJws(token)
                    .getBody();

            Long adminId = Long.valueOf(claims.getSubject());
            log.info("从Token中解析出管理员ID: {}", adminId);

            // 查询管理员信息
            AdminUser adminUser = adminUserMapper.selectById(adminId);
            if (adminUser == null) {
                log.error("管理员不存在: {}", adminId);
                throw new RuntimeException("用户不存在");
            }

            if (adminUser.getStatus() != 1) {
                log.error("管理员已被禁用: {}", adminId);
                throw new RuntimeException("用户已被禁用");
            }

            log.info("Token验证成功，管理员: {}", adminUser.getUsername());
            return adminUser;
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage(), e);
            throw new RuntimeException("Token无效: " + e.getMessage());
        }
    }

    @Override
    public String generateToken(AdminUser adminUser) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration * 1000);
        
        return Jwts.builder()
                .setSubject(adminUser.getId().toString())
                .claim("username", adminUser.getUsername())
                .claim("role", adminUser.getRole())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }

    @Override
    public void updateLastLogin(Long adminId, String ipAddress) {
        AdminUser adminUser = new AdminUser();
        adminUser.setId(adminId);
        adminUser.setLastLoginTime(LocalDateTime.now());
        adminUser.setLastLoginIp(ipAddress);
        adminUserMapper.updateById(adminUser);
    }

    @Override
    public void logAction(Long adminId, String adminUsername, String action, String resource, 
                         String resourceId, String description, String ipAddress, String userAgent) {
        AdminLog adminLog = new AdminLog();
        adminLog.setAdminId(adminId);
        adminLog.setAdminUsername(adminUsername);
        adminLog.setAction(action);
        adminLog.setResource(resource);
        adminLog.setResourceId(resourceId);
        adminLog.setDescription(description);
        adminLog.setIpAddress(ipAddress);
        adminLog.setUserAgent(userAgent);
        adminLog.setCreatedAt(LocalDateTime.now());
        
        adminLogMapper.insert(adminLog);
    }
}
