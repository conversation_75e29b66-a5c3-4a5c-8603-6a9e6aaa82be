package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格分类实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("price_categories")
public class PriceCategory {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 价格分类名称
     */
    private String name;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 状态：1启用，0禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
