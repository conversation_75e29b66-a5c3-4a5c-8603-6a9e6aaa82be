package com.flower.controller;

import com.flower.common.PageResult;
import com.flower.common.Result;
import com.flower.entity.Category;
import com.flower.entity.Flower;
import com.flower.vo.FlowerVO;
import com.flower.service.FlowerService;
import com.flower.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 花卉商品控制器
 */
@Slf4j
@RestController
@RequestMapping("/flower")
@CrossOrigin(origins = "*")
public class FlowerController {

    @Autowired
    private FlowerService flowerService;

    @Autowired
    private RegionService regionService;

    /**
     * 获取所有分类
     */
    @GetMapping("/categories")
    public Result<List<Category>> getCategories() {
        try {
            List<Category> categories = flowerService.getAllCategories();
            return Result.success(categories);
        } catch (Exception e) {
            log.error("获取分类失败", e);
            return Result.error("获取分类失败");
        }
    }

    /**
     * 分页获取花卉列表
     */
    @GetMapping("/list")
    public Result<PageResult<FlowerVO>> getFlowers(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword) {
        try {
            PageResult<FlowerVO> result = flowerService.getFlowersByPage(current, size, categoryId, keyword);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取花卉列表失败", e);
            return Result.error("获取花卉列表失败");
        }
    }

    /**
     * 获取花卉详情
     */
    @GetMapping("/detail/{id}")
    public Result<Flower> getFlowerDetail(@PathVariable Long id) {
        try {
            Flower flower = flowerService.getFlowerById(id);
            if (flower == null) {
                return Result.notFound("花卉不存在");
            }
            return Result.success(flower);
        } catch (Exception e) {
            log.error("获取花卉详情失败", e);
            return Result.error("获取花卉详情失败");
        }
    }

    /**
     * 获取精选花卉
     */
    @GetMapping("/featured")
    public Result<List<Flower>> getFeaturedFlowers(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Flower> flowers = flowerService.getFeaturedFlowers(limit);
            return Result.success(flowers);
        } catch (Exception e) {
            log.error("获取精选花卉失败", e);
            return Result.error("获取精选花卉失败");
        }
    }

    /**
     * 搜索花卉
     */
    @GetMapping("/search")
    public Result<PageResult<FlowerVO>> searchFlowers(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return Result.paramError("搜索关键词不能为空");
            }

            PageResult<FlowerVO> result = flowerService.searchFlowers(keyword, current, size);
            return Result.success(result);
        } catch (Exception e) {
            log.error("搜索花卉失败", e);
            return Result.error("搜索花卉失败");
        }
    }

    /**
     * 根据分类获取花卉
     */
    @GetMapping("/category/{categoryId}")
    public Result<PageResult<FlowerVO>> getFlowersByCategory(
            @PathVariable Long categoryId,
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        try {
            PageResult<FlowerVO> result = flowerService.getFlowersByCategory(categoryId, current, size);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据分类获取花卉失败", e);
            return Result.error("根据分类获取花卉失败");
        }
    }

    /**
     * 获取完整的省市区数据（用于前端选择器）
     */
    @GetMapping("/region/data")
    public Result<Map<String, Object>> getRegionData() {
        try {
            // 使用RegionService从数据库获取数据
            Map<String, Object> regionData = regionService.getRegionData();
            return Result.success(regionData);
        } catch (Exception e) {
            log.error("获取地区数据失败", e);
            return Result.error("获取地区数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门搜索关键词
     */
    @GetMapping("/hot-keywords")
    public Result<List<String>> getHotKeywords() {
        try {
            List<String> hotKeywords = flowerService.getHotKeywords();
            return Result.success(hotKeywords);
        } catch (Exception e) {
            log.error("获取热门搜索关键词失败", e);
            return Result.error("获取热门搜索关键词失败");
        }
    }
}
