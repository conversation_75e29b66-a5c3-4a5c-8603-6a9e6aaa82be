{"version": 3, "names": ["_helperValidatorIdentifier", "require", "isValidIdentifier", "name", "reserved", "isKeyword", "isStrictReservedWord", "isIdentifierName"], "sources": ["../../src/validators/isValidIdentifier.ts"], "sourcesContent": ["import {\n  isIdentifierName,\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\n\n/**\n * Check if the input `name` is a valid identifier name\n * and isn't a reserved word.\n */\nexport default function isValidIdentifier(\n  name: string,\n  reserved: boolean = true,\n): boolean {\n  if (typeof name !== \"string\") return false;\n\n  if (reserved) {\n    // \"await\" is invalid in module, valid in script; better be safe (see #4952)\n    if (isKeyword(name) || isStrictReservedWord(name, true)) {\n      return false;\n    }\n  }\n\n  return isIdentifierName(name);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,0BAAA,GAAAC,OAAA;AAUe,SAASC,iBAAiBA,CACvCC,IAAY,EACZC,QAAiB,GAAG,IAAI,EACf;EACT,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK;EAE1C,IAAIC,QAAQ,EAAE;IAEZ,IAAI,IAAAC,oCAAS,EAACF,IAAI,CAAC,IAAI,IAAAG,+CAAoB,EAACH,IAAI,EAAE,IAAI,CAAC,EAAE;MACvD,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAAI,2CAAgB,EAACJ,IAAI,CAAC;AAC/B", "ignoreList": []}